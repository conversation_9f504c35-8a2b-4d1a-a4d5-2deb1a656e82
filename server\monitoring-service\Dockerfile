# 构建阶段
FROM node:22-alpine AS build

WORKDIR /app

# 复制package.json和package-lock.json
COPY package*.json ./

# 安装依赖
RUN npm ci --legacy-peer-deps

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 运行阶段
FROM node:22-alpine

WORKDIR /app

# 安装必要的系统依赖
RUN apk add --no-cache curl

# 复制package.json和package-lock.json
COPY package*.json ./

# 安装生产依赖
RUN npm ci --legacy-peer-deps --only=production

# 从构建阶段复制构建结果
COPY --from=build /app/dist ./dist

# 创建日志目录
RUN mkdir -p /app/logs

# 设置环境变量
ENV NODE_ENV=production
ENV PORT=3100

# 暴露端口
EXPOSE 3100

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3100/api/v1/health || exit 1

# 启动应用
CMD ["node", "dist/main"]
