const fs = require('fs');
const path = require('path');

console.log('🔧 开始修复枚举错误...');

const distDir = path.join(__dirname, 'dist');
const assetsDir = path.join(distDir, 'assets');

if (!fs.existsSync(assetsDir)) {
    console.error('❌ assets 目录不存在');
    process.exit(1);
}

// 查找主 JavaScript 文件
const files = fs.readdirSync(assetsDir);
const mainJsFile = files.find(file => file.startsWith('main-') && file.endsWith('.js'));

if (!mainJsFile) {
    console.error('❌ 找不到主 JavaScript 文件');
    process.exit(1);
}

const mainJsPath = path.join(assetsDir, mainJsFile);
console.log(`📁 找到主文件: ${mainJsFile}`);

try {
    // 读取文件内容
    let content = fs.readFileSync(mainJsPath, 'utf8');
    console.log(`📖 文件大小: ${content.length} 字符`);
    
    // 记录修复前的问题
    const beforeMatches = content.match(/\bli\.VIE\b/g);
    console.log(`🔍 发现 li.VIE 引用: ${beforeMatches ? beforeMatches.length : 0} 个`);
    
    // 修复被压缩的枚举引用
    let fixCount = 0;
    
    // 修复 li.VIE -> CollaborationRole.VIEWER
    content = content.replace(/\bli\.VIE\b/g, () => {
        fixCount++;
        return 'window.CollaborationRole.VIEWER';
    });
    
    // 修复 li.VIEWER -> CollaborationRole.VIEWER
    content = content.replace(/\bli\.VIEWER\b/g, () => {
        fixCount++;
        return 'window.CollaborationRole.VIEWER';
    });
    
    // 修复其他可能的压缩问题
    content = content.replace(/\bli\.EDI\b/g, () => {
        fixCount++;
        return 'window.CollaborationRole.EDITOR';
    });
    
    content = content.replace(/\bli\.EDITOR\b/g, () => {
        fixCount++;
        return 'window.CollaborationRole.EDITOR';
    });
    
    content = content.replace(/\bli\.ADM\b/g, () => {
        fixCount++;
        return 'window.CollaborationRole.ADMIN';
    });
    
    content = content.replace(/\bli\.ADMIN\b/g, () => {
        fixCount++;
        return 'window.CollaborationRole.ADMIN';
    });
    
    content = content.replace(/\bli\.OWN\b/g, () => {
        fixCount++;
        return 'window.CollaborationRole.OWNER';
    });
    
    content = content.replace(/\bli\.OWNER\b/g, () => {
        fixCount++;
        return 'window.CollaborationRole.OWNER';
    });

    // 修复可能的赋值操作，避免只读属性错误
    content = content.replace(/(\w+)\.VIEWER\s*=\s*([^;]+)/g, (match, obj, value) => {
        fixCount++;
        console.log(`修复赋值操作: ${match}`);
        return `try { ${obj}.VIEWER = ${value}; } catch(e) { console.warn('VIEWER属性赋值被忽略:', e.message); }`;
    });

    content = content.replace(/(\w+)\.EDITOR\s*=\s*([^;]+)/g, (match, obj, value) => {
        fixCount++;
        return `try { ${obj}.EDITOR = ${value}; } catch(e) { console.warn('EDITOR属性赋值被忽略:', e.message); }`;
    });

    content = content.replace(/(\w+)\.ADMIN\s*=\s*([^;]+)/g, (match, obj, value) => {
        fixCount++;
        return `try { ${obj}.ADMIN = ${value}; } catch(e) { console.warn('ADMIN属性赋值被忽略:', e.message); }`;
    });

    content = content.replace(/(\w+)\.OWNER\s*=\s*([^;]+)/g, (match, obj, value) => {
        fixCount++;
        return `try { ${obj}.OWNER = ${value}; } catch(e) { console.warn('OWNER属性赋值被忽略:', e.message); }`;
    });
    
    // 检查是否已经有枚举修复脚本
    if (content.includes('// 🚀 枚举修复脚本')) {
        console.log('⚠️ 枚举修复脚本已存在，跳过注入');
        console.log(`✅ 修复了 ${fixCount} 个引用`);
        if (fixCount > 0) {
            fs.writeFileSync(mainJsPath, content, 'utf8');
            console.log('✅ 文件已更新');
        }
        return;
    }

    // 在文件开头添加枚举定义
    const enumDefinition = `// 🚀 枚举修复脚本 - 解决压缩后的枚举引用问题
(function() {
    'use strict';

    // 防止重复执行
    if (window.__ENUM_FIXED__) {
        return;
    }

    // 安全地定义枚举，使用 Object.defineProperty 确保属性可配置
    if (!window.CollaborationRole) {
        window.CollaborationRole = {};
    }

    // 定义所有枚举值，确保它们是可写的
    const roleValues = {
        'VIEWER': 'viewer',
        'EDITOR': 'editor',
        'ADMIN': 'admin',
        'OWNER': 'owner'
    };

    for (const [key, value] of Object.entries(roleValues)) {
        try {
            Object.defineProperty(window.CollaborationRole, key, {
                value: value,
                writable: true,
                enumerable: true,
                configurable: true
            });
        } catch (e) {
            // 如果属性已存在且不可配置，直接赋值
            try {
                window.CollaborationRole[key] = value;
            } catch (e2) {
                console.warn('无法设置枚举属性:', key, e2.message);
            }
        }
    }

    if (!window.Permission) {
        window.Permission = {};
        const permissions = {
            VIEW_SCENE: 'view_scene',
            EDIT_SCENE: 'edit_scene',
            CREATE_ENTITY: 'create_entity',
            UPDATE_ENTITY: 'update_entity',
            DELETE_ENTITY: 'delete_entity',
            ADD_COMPONENT: 'add_component',
            UPDATE_COMPONENT: 'update_component',
            REMOVE_COMPONENT: 'remove_component',
            UPLOAD_ASSET: 'upload_asset',
            SAVE_SCENE: 'save_scene',
            EXPORT_SCENE: 'export_scene',
            IMPORT_SCENE: 'import_scene',
            ASSIGN_ROLES: 'assign_roles',
            MANAGE_PERMISSIONS: 'manage_permissions'
        };

        for (const [key, value] of Object.entries(permissions)) {
            Object.defineProperty(window.Permission, key, {
                value: value,
                writable: true,
                enumerable: true,
                configurable: true
            });
        }
    }

    // 创建 li 对象来处理压缩后的引用，使用简单对象避免 Proxy 问题
    if (!window.li) {
        window.li = {};
    }

    // 定义映射
    const mapping = {
        'VIE': 'viewer',
        'VIEWER': 'viewer',
        'EDI': 'editor',
        'EDITOR': 'editor',
        'ADM': 'admin',
        'ADMIN': 'admin',
        'OWN': 'owner',
        'OWNER': 'owner'
    };

    // 为每个映射创建可配置的属性
    for (const [key, value] of Object.entries(mapping)) {
        try {
            Object.defineProperty(window.li, key, {
                value: value,
                writable: true,
                enumerable: true,
                configurable: true
            });
        } catch (e) {
            // 如果属性已存在且不可配置，直接赋值
            try {
                window.li[key] = value;
            } catch (e2) {
                console.warn('无法设置li属性:', key, e2.message);
            }
        }
    }

    // 标记修复完成
    window.__ENUM_FIXED__ = true;
    console.log('✅ 枚举修复完成');
})();

`;
    
    content = enumDefinition + content;
    
    // 写回文件
    fs.writeFileSync(mainJsPath, content, 'utf8');
    
    console.log(`✅ 修复完成! 共修复 ${fixCount} 个引用`);
    console.log(`📝 文件已更新: ${mainJsPath}`);

    // 验证修复结果
    const afterMatches = content.match(/\bli\.VIE\b/g);
    console.log(`🔍 修复后剩余 li.VIE 引用: ${afterMatches ? afterMatches.length : 0} 个`);

    // 更新 HTML 文件，注入修复脚本
    const htmlPath = path.join(distDir, 'index.html');
    if (fs.existsSync(htmlPath)) {
        console.log('📄 更新 HTML 文件...');
        let htmlContent = fs.readFileSync(htmlPath, 'utf8');

        // 检查是否已经注入过修复脚本
        if (!htmlContent.includes('枚举修复脚本')) {
            // 在 </head> 标签前注入修复脚本
            const fixScript = `
    <!-- 枚举修复脚本 -->
    <script>
${enumDefinition.trim()}
    </script>`;

            htmlContent = htmlContent.replace('</head>', fixScript + '\n  </head>');
            fs.writeFileSync(htmlPath, htmlContent, 'utf8');
            console.log('✅ HTML 文件已更新');
        } else {
            console.log('ℹ️ HTML 文件已包含修复脚本');
        }
    }

} catch (error) {
    console.error('❌ 修复过程中出错:', error);
    process.exit(1);
}

console.log('🎉 枚举修复脚本执行完成!');
