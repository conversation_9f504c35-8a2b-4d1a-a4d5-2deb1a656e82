/**
 * 协作相关类型定义
 */

// 协作状态枚举
export enum CollaborationStatus {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error'
}

// 用户角色枚举 - 使用普通对象而不是const断言，以允许属性可写
export const CollaborationRole = {
  VIEWER: 'viewer',
  EDITOR: 'editor',
  ADMIN: 'admin',
  OWNER: 'owner'
} as {
  VIEWER: 'viewer';
  EDITOR: 'editor';
  ADMIN: 'admin';
  OWNER: 'owner';
};

// 导出类型
export type CollaborationRole = 'viewer' | 'editor' | 'admin' | 'owner';

// 消息类型枚举
export enum MessageType {
  JOIN = 'join',
  LEAVE = 'leave',
  OPERATION = 'operation',
  USER_STATUS = 'user_status',
  CURSOR_MOVE = 'cursor_move',
  SELECTION_CHANGE = 'selection_change',
  BATCH_OPERATIONS = 'batch_operations',
  HEARTBEAT = 'heartbeat',
  ERROR = 'error',
  // 扩展的消息类型
  USER_LIST = 'user_list',
  USER_JOINED = 'user_joined',
  USER_LEFT = 'user_left',
  OPERATION_HISTORY = 'operation_history'
}

// 操作类型枚举
export enum OperationType {
  ENTITY_CREATE = 'entity_create',
  ENTITY_UPDATE = 'entity_update',
  ENTITY_DELETE = 'entity_delete',
  COMPONENT_ADD = 'component_add',
  COMPONENT_UPDATE = 'component_update',
  COMPONENT_REMOVE = 'component_remove',
  SCENE_UPDATE = 'scene_update',
  CURSOR_MOVE = 'cursor_move',
  SELECTION_CHANGE = 'selection_change',
  // 扩展的操作类型
  PROPERTY_UPDATE = 'property_update',
  EDITING_ZONE_UPDATE = 'editing_zone_update',
  EDITING_ZONE_CLEAR = 'editing_zone_clear',
  LOCK_CREATE = 'lock_create',
  LOCK_RELEASE = 'lock_release',
  LOCK_RENEW = 'lock_renew',
  LOCK_FORCIBLY_RELEASE = 'lock_forcibly_release',
  // 新增的操作类型，用于意图预测
  CAMERA_MOVE = 'camera_move',
  ENTITY_HOVER = 'entity_hover',
  COMPONENT_HOVER = 'component_hover',
  PROPERTY_HOVER = 'property_hover'
}

// 用户状态接口
export interface CollaborationUser {
  id: string;
  name: string;
  avatar?: string;
  role: CollaborationRole;
  color: string;
  isActive: boolean;
  lastActivity: number;
  cursor?: {
    x: number;
    y: number;
    entityId?: string;
  };
}

// 操作接口
export interface Operation {
  id: string;
  type: OperationType;
  userId: string;
  timestamp: number;
  data: any;
  entityId?: string;
  componentType?: string;
}

// 消息接口
export interface Message {
  type: MessageType;
  data: any;
}

// 加入消息数据
export interface JoinMessageData {
  userId: string;
  userName: string;
  projectId: string;
  sceneId: string;
}

// 用户状态数据
export interface UserStatusData {
  userId: string;
  isActive: boolean;
  lastActivity: number;
  cursor?: {
    x: number;
    y: number;
    entityId?: string;
  };
}
