FROM node:22-alpine AS builder

WORKDIR /app

COPY package*.json ./

RUN npm install

COPY . .

RUN npm run build

FROM node:22-alpine

WORKDIR /app

COPY --from=builder /app/package*.json ./
COPY --from=builder /app/dist ./dist

RUN npm install --only=production

# 创建上传目录
RUN mkdir -p /app/uploads/images /app/uploads/models /app/uploads/audio /app/uploads/other

EXPOSE 3000

CMD ["node", "dist/main.js"]
